# Curemate Health Hub

A modern healthcare e-commerce website built with vanilla HTML, CSS, and JavaScript. This project showcases a clean, responsive design with interactive components for an engaging user experience.

## Features

- Responsive design that works on mobile, tablet, and desktop
- Interactive components using vanilla JavaScript
- Bootstrap 5 for layout and components
- Custom CSS for a cohesive design system
- Animations and transitions for a modern user experience

## Pages

- **Home Page**: Main landing page with multiple sections
  - Navigation bar with dropdown menus
  - Hero section with call-to-action buttons
  - Category browsing section
  - Product carousel
  - Health concerns section
  - Trending products section
  - About section
  - Footer with newsletter subscription

- **404 Page**: Custom error page

## Project Structure

```
curemate-health-hub/
├── index.html          # Main entry point
├── 404.html            # 404 error page
├── css/
│   └── styles.css      # Custom styles
├── js/
│   └── main.js         # JavaScript functionality
├── img/                # Image assets
├── favicon.ico         # Website favicon
├── robots.txt          # Search engine instructions
└── README.md           # Project documentation
```

## Technologies Used

- **HTML5**: Structure and semantic markup
- **CSS3**: Custom styling and animations
- **JavaScript**: Interactive elements and functionality
- **Bootstrap 5**: Responsive grid system and components
- **Font Awesome**: Icons

## Browser Compatibility

This website is compatible with:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Opera (latest)

## Development

To run this project locally, simply clone the repository and open `index.html` in your browser.

```bash
git clone <repository-url>
cd curemate-health-hub
```

## Credits

- Font Awesome for icons
- Bootstrap for the responsive framework
