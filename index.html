<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Curemate Health Hub</title>
    <meta name="description" content="Curemate Health Hub - Your Healthcare Partner" />
    <meta name="author" content="Curemate" />

    <meta property="og:title" content="Curemate Health Hub" />
    <meta property="og:description" content="Curemate Health Hub - Your Healthcare Partner" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="img/hero-section.jpg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@curemate" />
    <meta name="twitter:image" content="img/hero-section.jpg.png" />
    
    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
  </head>

  <body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg sticky-top navbar-light border-bottom">
      <div class="container">
        <!-- Logo -->
        <a class="navbar-brand d-flex align-items-center" href="#">
          <div class="bg-primary p-2 rounded-3 me-2">
            <i class="fa-solid fa-heart text-white"></i>
          </div>
          <span class="fw-bold text-primary">Curemate</span>
        </a>
        
        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent">
          <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Navbar Content -->
        <div class="collapse navbar-collapse" id="navbarContent">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item">
              <a class="nav-link" href="#about">About Us</a>
            </li>
          </ul>
          
          <!-- Right Actions -->
          <div class="d-flex align-items-center gap-3">
            <a href="#" class="btn btn-link text-decoration-none text-dark d-none d-md-flex align-items-center">
              <i class="fa-solid fa-location-dot me-1"></i> Find a Store
            </a>
            
            <a href="#" class="btn btn-outline-primary d-none d-md-flex align-items-center">
              <i class="fa-solid fa-store me-1"></i> Vendor Signup/Login
            </a>
            
            <a href="#" class="btn btn-link text-decoration-none text-dark d-none d-md-flex align-items-center">
              <i class="fa-solid fa-phone me-1"></i> Contact Us
            </a>
            
            <!-- Cart Icon -->
            <div class="position-relative">
              <i class="fa-solid fa-cart-shopping"></i>
              <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-accent">
                3
              </span>
            </div>
            
            <a href="#" class="btn btn-primary d-flex align-items-center">
              <i class="fa-solid fa-user me-2"></i> Login
            </a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section py-5 bg-hero-gradient">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6 mb-4 mb-lg-0">
            <h1 class="display-4 fw-bold mb-4">Discounts!! that cure a part of your sickness.</h1>
            <p class="lead mb-4">Discover a wide range of healthcare products and services designed to keep you and your family healthy.</p>
            <div class="d-flex gap-3">
              <a href="#" class="btn btn-primary btn-lg">Shop Now</a>
              <a href="#" class="btn btn-outline-primary btn-lg">Learn More</a>
            </div>
          </div>
          <div class="col-lg-6">
            <img src="img/hero-section.jpg.png" alt="Healthcare Products" class="img-fluid rounded-4 shadow" style="width: 100%; object-fit: cover;">
          </div>
        </div>
      </div>
    </section>

    <!-- Search and Prescription Section -->
    <section class="search-section py-4">
      <div class="container">
        <!-- Search Bar -->
        <div class="search-container">
          <h2 class="text-center mb-4 fw-bold">What are you looking for?</h2>
          <div class="search-bar">
            <input type="text" class="search-input" placeholder="Search for Medicine">
            <button class="btn btn-primary search-btn">Search</button>
          </div>
        </div>
        
        <!-- Prescription Upload -->
        <div class="prescription-upload mt-3">
          <div class="prescription-text">
            <i class="fa-solid fa-file-prescription"></i>
            Order with prescription.
          </div>
          <input type="file" id="prescription-file" accept=".pdf,.jpg,.jpeg,.png" style="display: none;" onchange="updateFileName(this)">
          <div class="d-flex align-items-center">
            <span id="selected-file" class="me-3 text-muted" style="font-size: 0.9rem;"></span>
            <div class="d-flex gap-2">
              <a href="#" class="upload-btn" onclick="document.getElementById('prescription-file').click(); return false;">
                UPLOAD NOW
                <i class="fa-solid fa-arrow-right"></i>
              </a>
              <a href="#" class="upload-btn text-danger" id="delete-file" onclick="deleteFile(); return false;" style="display: none;">
                <i class="fa-solid fa-trash"></i>
              </a>
            </div>
          </div>
        </div>

        <script>
          function updateFileName(input) {
            const fileNameDisplay = document.getElementById('selected-file');
            const deleteButton = document.getElementById('delete-file');
            if (input.files && input.files[0]) {
              const fileName = input.files[0].name;
              fileNameDisplay.textContent = fileName;
              deleteButton.style.display = 'flex';
            }
          }

          function deleteFile() {
            const fileInput = document.getElementById('prescription-file');
            const fileNameDisplay = document.getElementById('selected-file');
            const deleteButton = document.getElementById('delete-file');
            fileInput.value = '';
            fileNameDisplay.textContent = '';
            deleteButton.style.display = 'none';
          }
        </script>
      </div>
    </section>

    <!-- Category Section -->
    <section class="category-section py-5">
      <div class="container">
        <h2 class="section-title text-center mb-5">Shop by Category</h2>
        <div class="row g-4">
          <div class="col-6 col-md-3">
            <div class="category-card hover-lift p-0 d-flex flex-column align-items-stretch">
              <img src="img/medicines.jpg.png" class="w-100" alt="Medicines" style="height:320px; object-fit:cover; border-radius:0.75rem 0.75rem 0 0;" />
              <div class="p-4 text-center flex-grow-1 d-flex flex-column justify-content-center">
                <h3 class="category-title mb-2">Medicines</h3>
                <p class="category-count mb-0">1200+ Products</p>
              </div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="category-card hover-lift p-0 d-flex flex-column align-items-stretch">
              <img src="img/healthcare.jpg.png" class="w-100" alt="Healthcare" style="height:320px; object-fit:cover; border-radius:0.75rem 0.75rem 0 0;" />
              <div class="p-4 text-center flex-grow-1 d-flex flex-column justify-content-center">
                <h3 class="category-title mb-2">Healthcare</h3>
                <p class="category-count mb-0">800+ Products</p>
              </div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="category-card hover-lift p-0 d-flex flex-column align-items-stretch">
              <img src="img/wellness.jpg.png" class="w-100" alt="Wellness" style="height:320px; object-fit:cover; border-radius:0.75rem 0.75rem 0 0;" />
              <div class="p-4 text-center flex-grow-1 d-flex flex-column justify-content-center">
                <h3 class="category-title mb-2">Wellness</h3>
                <p class="category-count mb-0">500+ Products</p>
              </div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="category-card hover-lift p-0 d-flex flex-column align-items-stretch">
              <img src="img/babycare.jpg.png" class="w-100" alt="Baby Care" style="height:320px; object-fit:cover; border-radius:0.75rem 0.75rem 0 0;" />
              <div class="p-4 text-center flex-grow-1 d-flex flex-column justify-content-center">
                <h3 class="category-title mb-2">Baby Care</h3>
                <p class="category-count mb-0">300+ Products</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Image Carousel -->
    <section class="carousel-section py-5 bg-light">
      <div class="container position-relative">
        <h2 class="section-title text-center mb-5">Featured Products</h2>
        <div id="featuredCarousel" class="carousel slide" data-bs-ride="carousel">
          <div class="carousel-inner">
            <div class="carousel-item active">
              <div class="row g-4">
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/multivitamin.jpg.png" class="card-img-top" alt="Multivitamin Complex" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">Multivitamin Complex</h5>
                      <p class="card-text">Essential vitamins for daily health</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹499</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/thermometer.jpg.png" class="card-img-top" alt="Digital Thermometer" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">Digital Thermometer</h5>
                      <p class="card-text">Accurate temperature readings</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹299</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/first-aid.jpg.png" class="card-img-top" alt="First Aid Kit" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">First Aid Kit</h5>
                      <p class="card-text">Complete emergency care kit</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹799</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item">
              <div class="row g-4">
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/omega-3-supplements.jpg.png" class="card-img-top" alt="Omega-3 Supplements" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">Omega-3 Supplements</h5>
                      <p class="card-text">Heart health support</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹599</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/blood-pressure-monitor.jpg.png" class="card-img-top" alt="Blood Pressure Monitor" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">Blood Pressure Monitor</h5>
                      <p class="card-text">Digital BP monitoring device</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹1299</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/protein-powder.jpg.png" class="card-img-top" alt="Protein Powder" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">Protein Powder</h5>
                      <p class="card-text">Plant-based protein supplement</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹899</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item">
              <div class="row g-4">
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/scalpe.jpg.png" class="card-img-top" alt="Scalpe Plus Anti-Dandruff Shampoo" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">Scalpe Plus Anti-Dandruff Shampoo</h5>
                      <p class="card-text">Anti-dandruff hair care solution</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹281.82</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/hansaplast.jpg.png" class="card-img-top" alt="Hansaplast Bandages" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">Hansaplast Bandages</h5>
                      <p class="card-text">Sterile wound protection</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹146.25</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="product-card">
                    <img src="img/liveasy-seeds.jpg.png" class="card-img-top" alt="Liveasy Healthy Roasted Seed" style="height: 200px; object-fit: contain;">
                    <div class="card-body">
                      <h5 class="card-title">Liveasy Healthy Roasted Seed</h5>
                      <p class="card-text">Nutritious snack mix</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="price">₹175.00</span>
                        <button class="btn btn-sm btn-primary">Add to Cart</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button class="carousel-control-prev" type="button" data-bs-target="#featuredCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#featuredCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>
        </div>
      </div>
    </section>

    <!-- Health Concern Section -->
    <section class="health-concern-section py-5">
      <div class="container">
        <h2 class="section-title text-center mb-5">Shop by Health Concern</h2>
        <div class="row g-4">
          <div class="col-6 col-md-3">
            <div class="health-concern-card hover-lift p-0 d-flex flex-column align-items-stretch">
              <img src="img/heart-health.jpg.png" class="w-100" alt="Heart Health" style="height:320px; object-fit:cover; border-radius:0.75rem 0.75rem 0 0;" />
              <div class="p-4 text-center flex-grow-1 d-flex flex-column justify-content-center">
                <h3 class="concern-title mb-2">Heart Health</h3>
              </div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="health-concern-card hover-lift p-0 d-flex flex-column align-items-stretch">
              <img src="img/brain-health.jpg.png" class="w-100" alt="Brain Health" style="height:320px; object-fit:cover; border-radius:0.75rem 0.75rem 0 0;" />
              <div class="p-4 text-center flex-grow-1 d-flex flex-column justify-content-center">
                <h3 class="concern-title mb-2">Brain Health</h3>
              </div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="health-concern-card hover-lift p-0 d-flex flex-column align-items-stretch">
              <img src="img/bone-health.jpg.png" class="w-100" alt="Bone Health" style="height:320px; object-fit:cover; border-radius:0.75rem 0.75rem 0 0;" />
              <div class="p-4 text-center flex-grow-1 d-flex flex-column justify-content-center">
                <h3 class="concern-title mb-2">Bone Health</h3>
              </div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="health-concern-card hover-lift p-0 d-flex flex-column align-items-stretch">
              <img src="img/eyecare.jpg.png" class="w-100" alt="Eye Care" style="height:320px; object-fit:cover; border-radius:0.75rem 0.75rem 0 0;" />
              <div class="p-4 text-center flex-grow-1 d-flex flex-column justify-content-center">
                <h3 class="concern-title mb-2">Eye Care</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Trending Section -->
    <section class="trending-section py-5 bg-light">
      <div class="container">
        <h2 class="section-title text-center mb-5">Trending Products</h2>
        <div class="row g-4">
          <div class="col-md-3 col-6">
            <div class="product-card hover-lift">
              <div class="badge bg-accent position-absolute top-0 end-0 m-2">New</div>
              <img src="img/Immunity-Booster.jpg.png" class="card-img-top" alt="Immunity Booster" style="height: 200px; object-fit: contain;">
              <div class="card-body">
                <h5 class="card-title">Immunity Booster</h5>
                <div class="rating mb-2">
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star-half-stroke text-warning"></i>
                  <span class="ms-1">4.5</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                  <span class="price">₹699</span>
                  <button class="btn btn-sm btn-primary">Add to Cart</button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-6">
            <div class="product-card hover-lift">
              <div class="badge bg-success position-absolute top-0 end-0 m-2">Sale</div>
              <img src="img/Collagen-Peptide.jpg.png" class="card-img-top" alt="Collagen Peptides" style="height: 200px; object-fit: contain;">
              <div class="card-body">
                <h5 class="card-title">Collagen Peptides</h5>
                <div class="rating mb-2">
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <span class="ms-1">5.0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                  <span class="price">₹999</span>
                  <button class="btn btn-sm btn-primary">Add to Cart</button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-6">
            <div class="product-card hover-lift">
              <img src="img/Glucometer-Kit.jpg.png" class="card-img-top" alt="Glucometer Kit" style="height: 200px; object-fit: contain;">
              <div class="card-body">
                <h5 class="card-title">Glucometer Kit</h5>
                <div class="rating mb-2">
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-regular fa-star text-warning"></i>
                  <span class="ms-1">4.0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                  <span class="price">₹1499</span>
                  <button class="btn btn-sm btn-primary">Add to Cart</button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-6">
            <div class="product-card hover-lift">
              <div class="badge bg-accent position-absolute top-0 end-0 m-2">Best Seller</div>
              <img src="img/Vitamin-D3-Drops.jpg.png" class="card-img-top" alt="Vitamin D3 Drops" style="height: 200px; object-fit: contain;">
              <div class="card-body">
                <h5 class="card-title">Vitamin D3 Drops</h5>
                <div class="rating mb-2">
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star text-warning"></i>
                  <i class="fa-solid fa-star-half-stroke text-warning"></i>
                  <span class="ms-1">4.7</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                  <span class="price">₹399</span>
                  <button class="btn btn-sm btn-primary">Add to Cart</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section py-5">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6 mb-4 mb-lg-0">
            <img src="img/promo-banner-1.jpg" alt="About Curemate" class="img-fluid rounded-4 shadow">
          </div>
          <div class="col-lg-6">
            <h2 class="section-title mb-4">About Curemate</h2>
            <p class="lead mb-4">We're dedicated to providing quality healthcare products and services to improve the well-being of our customers.</p>
            <div class="row g-4 mb-4">
              <div class="col-6">
                <div class="feature-item">
                  <div class="feature-icon bg-primary-light text-primary">
                    <i class="fa-solid fa-truck-fast"></i>
                  </div>
                  <h4 class="feature-title">Fast Delivery</h4>
                  <p>Get your medicines delivered within hours</p>
                </div>
              </div>
              <div class="col-6">
                <div class="feature-item">
                  <div class="feature-icon bg-primary-light text-primary">
                    <i class="fa-solid fa-certificate"></i>
                  </div>
                  <h4 class="feature-title">Quality Assured</h4>
                  <p>All products are verified for authenticity</p>
                </div>
              </div>
            </div>
            <a href="#" class="btn btn-primary">Learn More About Us</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer py-5 bg-dark text-white">
      <div class="container">
        <div class="row g-4">
          <div class="col-lg-4 col-md-6">
            <div class="d-flex align-items-center mb-3">
              <div class="bg-primary p-2 rounded-3 me-2">
                <i class="fa-solid fa-heart text-white"></i>
              </div>
              <span class="fw-bold text-primary fs-4">Curemate</span>
            </div>
            <p class="mb-4">Your trusted partner for all healthcare needs. We provide quality products and exceptional service.</p>
            <div class="social-links">
              <a href="#" class="me-3 text-white"><i class="fab fa-facebook-f"></i></a>
              <a href="#" class="me-3 text-white"><i class="fab fa-twitter"></i></a>
              <a href="#" class="me-3 text-white"><i class="fab fa-instagram"></i></a>
              <a href="#" class="text-white"><i class="fab fa-linkedin-in"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-6">
            <h5 class="mb-4">Quick Links</h5>
            <ul class="list-unstyled">
              <li class="mb-2"><a href="#" class="text-decoration-none text-white">Home</a></li>
              <li class="mb-2"><a href="#" class="text-decoration-none text-white">About Us</a></li>
              <li class="mb-2"><a href="#" class="text-decoration-none text-white">Products</a></li>
              <li class="mb-2"><a href="#" class="text-decoration-none text-white">Contact</a></li>
            </ul>
          </div>
          <div class="col-lg-2 col-md-6">
            <h5 class="mb-4">Categories</h5>
            <ul class="list-unstyled">
              <li class="mb-2"><a href="#" class="text-decoration-none text-white">Medicines</a></li>
              <li class="mb-2"><a href="#" class="text-decoration-none text-white">Healthcare</a></li>
              <li class="mb-2"><a href="#" class="text-decoration-none text-white">Wellness</a></li>
              <li class="mb-2"><a href="#" class="text-decoration-none text-white">Baby Care</a></li>
            </ul>
          </div>
          <div class="col-lg-4 col-md-6">
            <h5 class="mb-4">Contact Us</h5>
            <ul class="list-unstyled">
              <li class="mb-2"><i class="fa-solid fa-location-dot me-2"></i> 123 Healthcare Street, Mumbai, India</li>
              <li class="mb-2"><i class="fa-solid fa-phone me-2"></i> +91 **********</li>
              <li class="mb-2"><i class="fa-solid fa-envelope me-2"></i> <EMAIL></li>
            </ul>
            <div class="mt-4">
              <h6 class="mb-3">Subscribe to our newsletter</h6>
              <div class="input-group">
                <input type="email" class="form-control" placeholder="Your email">
                <button class="btn btn-primary" type="button">Subscribe</button>
              </div>
            </div>
          </div>
        </div>
        <hr class="my-4 border-secondary">
        <div class="row">
          <div class="col-md-6 mb-3 mb-md-0">
            <p class="mb-0">&copy; 2023 Curemate Health Hub. All rights reserved.</p>
          </div>
          <div class="col-md-6 text-md-end">
            <a href="#" class="text-decoration-none text-white me-3">Privacy Policy</a>
            <a href="#" class="text-decoration-none text-white me-3">Terms of Service</a>
            <a href="#" class="text-decoration-none text-white">Sitemap</a>
          </div>
        </div>
      </div>
    </footer>

    <!-- 404 Page Template (Hidden) -->
    <div id="notFoundTemplate" style="display: none;">
      <div class="min-vh-100 d-flex align-items-center justify-content-center bg-light">
        <div class="text-center">
          <h1 class="display-1 fw-bold">404</h1>
          <p class="fs-4 text-secondary mb-4">Oops! Page not found</p>
          <a href="index.html" class="btn btn-primary">Return to Home</a>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/main.js"></script>
  </body>
</html>
